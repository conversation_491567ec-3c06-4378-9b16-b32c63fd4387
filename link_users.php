<?php
require_once 'config.php';
checkAdmin();

$message = '';
$messageType = 'success';

// معالجة ربط المستخدم بالموظف
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action']) && $_POST['action'] == 'link') {
        $user_id = intval($_POST['user_id']);
        $employee_id = intval($_POST['employee_id']);
        
        try {
            // التحقق من عدم ربط الموظف بمستخدم آخر
            $check_stmt = $pdo->prepare("SELECT id, username FROM users WHERE employee_id = ? AND id != ?");
            $check_stmt->execute([$employee_id, $user_id]);
            $existing_link = $check_stmt->fetch();
            
            if ($existing_link) {
                $message = 'هذا الموظف مرتبط بالفعل بالمستخدم: ' . $existing_link['username'];
                $messageType = 'warning';
            } else {
                // ربط المستخدم بالموظف
                $stmt = $pdo->prepare("UPDATE users SET employee_id = ? WHERE id = ?");
                $stmt->execute([$employee_id, $user_id]);
                $message = 'تم ربط المستخدم بالموظف بنجاح';
            }
        } catch (Exception $e) {
            $message = 'خطأ في ربط المستخدم: ' . $e->getMessage();
            $messageType = 'danger';
        }
    } elseif (isset($_POST['action']) && $_POST['action'] == 'unlink') {
        $user_id = intval($_POST['user_id']);
        
        try {
            $stmt = $pdo->prepare("UPDATE users SET employee_id = NULL WHERE id = ?");
            $stmt->execute([$user_id]);
            $message = 'تم إلغاء ربط المستخدم بنجاح';
        } catch (Exception $e) {
            $message = 'خطأ في إلغاء الربط: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

// جلب المستخدمين مع بيانات الموظفين المرتبطين
$users_stmt = $pdo->query("
    SELECT u.*, e.name as employee_name, e.employee_id as emp_id 
    FROM users u 
    LEFT JOIN employees e ON u.employee_id = e.id 
    WHERE u.role = 'user'
    ORDER BY u.username
");
$users = $users_stmt->fetchAll();

// جلب الموظفين غير المرتبطين
$employees_stmt = $pdo->query("
    SELECT e.* 
    FROM employees e 
    LEFT JOIN users u ON e.id = u.employee_id 
    WHERE u.employee_id IS NULL
    ORDER BY e.name
");
$available_employees = $employees_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ربط المستخدمين بالموظفين - مجمع سيد الشهداء (عليه السلام) الخدمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .linked {
            background-color: #d4edda;
        }
        .unlinked {
            background-color: #fff3cd;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                مجمع سيد الشهداء (عليه السلام) الخدمي
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="users.php">
                    <i class="fas fa-users me-1"></i>المستخدمين
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-link me-2"></i>ربط المستخدمين بالموظفين</h2>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- شرح -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>ملاحظة:</strong> ربط المستخدمين بالموظفين يسمح للموظفين بالدخول للنظام ورؤية رصيدهم وإجازاتهم فقط.
                </div>

                <!-- قائمة المستخدمين -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-users me-2"></i>المستخدمين وحالة الربط</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($users)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا يوجد مستخدمين</h5>
                                <a href="users.php" class="btn btn-primary">إضافة مستخدم جديد</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>اسم المستخدم</th>
                                            <th>الموظف المرتبط</th>
                                            <th>معرف الموظف</th>
                                            <th>الحالة</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($users as $user): ?>
                                            <tr class="<?php echo $user['employee_id'] ? 'linked' : 'unlinked'; ?>">
                                                <td>
                                                    <i class="fas fa-user me-2"></i>
                                                    <?php echo htmlspecialchars($user['username']); ?>
                                                </td>
                                                <td>
                                                    <?php if ($user['employee_name']): ?>
                                                        <i class="fas fa-check-circle text-success me-2"></i>
                                                        <?php echo htmlspecialchars($user['employee_name']); ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">غير مرتبط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo $user['emp_id'] ? htmlspecialchars($user['emp_id']) : '-'; ?>
                                                </td>
                                                <td>
                                                    <?php if ($user['employee_id']): ?>
                                                        <span class="badge bg-success">مرتبط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-warning">غير مرتبط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if ($user['employee_id']): ?>
                                                        <form method="POST" class="d-inline">
                                                            <input type="hidden" name="action" value="unlink">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                                    onclick="return confirm('هل تريد إلغاء ربط هذا المستخدم؟')">
                                                                <i class="fas fa-unlink me-1"></i>إلغاء الربط
                                                            </button>
                                                        </form>
                                                    <?php else: ?>
                                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                onclick="showLinkModal(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                            <i class="fas fa-link me-1"></i>ربط بموظف
                                                        </button>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج ربط المستخدم بموظف -->
    <div class="modal fade" id="linkModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">ربط المستخدم بموظف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="link">
                        <input type="hidden" name="user_id" id="link_user_id">
                        
                        <div class="mb-3">
                            <label class="form-label">المستخدم</label>
                            <input type="text" class="form-control" id="link_username" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="employee_id" class="form-label">اختر الموظف</label>
                            <select class="form-select" name="employee_id" id="employee_id" required>
                                <option value="">اختر الموظف</option>
                                <?php foreach ($available_employees as $employee): ?>
                                    <option value="<?php echo $employee['id']; ?>">
                                        <?php echo htmlspecialchars($employee['name']) . ' - ' . htmlspecialchars($employee['employee_id']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <?php if (empty($available_employees)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                جميع الموظفين مرتبطين بالفعل أو لا يوجد موظفين متاحين.
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary" <?php echo empty($available_employees) ? 'disabled' : ''; ?>>
                            <i class="fas fa-link me-1"></i>ربط
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showLinkModal(userId, username) {
            document.getElementById('link_user_id').value = userId;
            document.getElementById('link_username').value = username;
            new bootstrap.Modal(document.getElementById('linkModal')).show();
        }
    </script>
</body>
</html>
