<?php
require_once 'config.php';
checkLogin();

// معالجة الفلاتر
$report_date = isset($_GET['report_date']) ? $_GET['report_date'] : date('Y/m/d');
$department = isset($_GET['department']) ? $_GET['department'] : '';

// جلب الموظفين
$whereConditions = [];
$params = [];

if ($_SESSION['role'] != 'admin') {
    $whereConditions[] = "user_id = ?";
    $params[] = $_SESSION['user_id'];
}

if ($department) {
    $whereConditions[] = "department = ?";
    $params[] = $department;
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

$stmt = $pdo->prepare("
    SELECT employee_id, name, department
    FROM employees
    {$whereClause}
    ORDER BY employee_id
");
$stmt->execute($params);
$employees = $stmt->fetchAll();

// حساب الإحصائيات للصفحة الثانية
$statsQuery = "
    SELECT
        COUNT(DISTINCT e.id) as total_employees,
        COUNT(CASE WHEN a.status = 'حاضر' THEN 1 END) as present_count,
        COUNT(CASE WHEN a.status = 'غائب' THEN 1 END) as absent_count,
        COUNT(CASE WHEN a.status = 'سنوية' THEN 1 END) as annual_leave_count,
        COUNT(CASE WHEN a.status = 'بدون راتب' THEN 1 END) as unpaid_leave_count,
        COUNT(CASE WHEN a.status = 'استخدام' THEN 1 END) as usage_count,
        COUNT(CASE WHEN a.status = 'وفاة' THEN 1 END) as death_count
    FROM employees e
    LEFT JOIN attendance a ON e.id = a.employee_id AND a.date = ?
    " . ($whereClause ? str_replace("WHERE", "WHERE", $whereClause) : "") . "
";

// تحضير المعاملات للاستعلام
$statsParams = [$report_date];
if (!empty($params)) {
    $statsParams = array_merge($statsParams, $params);
}

$statsStmt = $pdo->prepare($statsQuery);
$statsStmt->execute($statsParams);
$stats = $statsStmt->fetch();

// التأكد من وجود البيانات
if (!$stats) {
    $stats = [
        'total_employees' => 0,
        'present_count' => 0,
        'absent_count' => 0,
        'annual_leave_count' => 0,
        'unpaid_leave_count' => 0,
        'usage_count' => 0,
        'death_count' => 0
    ];
}

// جلب الأقسام للفلترة
if ($_SESSION['role'] == 'admin') {
    $deptStmt = $pdo->query("SELECT DISTINCT department FROM employees ORDER BY department");
} else {
    $deptStmt = $pdo->prepare("SELECT DISTINCT department FROM employees WHERE user_id = ? ORDER BY department");
    $deptStmt->execute([$_SESSION['user_id']]);
}
$departments = $deptStmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الموظفين - مجمع سيد الشهداء (عليه السلام) الخدمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .report-container {
            background: white;
            margin: 20px auto;
            max-width: 800px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .report-header {
            text-align: center;
            padding: 20px;
            border-bottom: 2px solid #000;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #000;
            font-size: 14px;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .report-table th,
        .report-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 12px;
        }
        
        .report-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .id-column {
            width: 80px;
        }
        
        .name-column {
            width: 200px;
        }
        
        @media print {
            @page {
                size: A4;
                margin: 1cm;
            }

            body {
                background: white;
            }

            .no-print {
                display: none !important;
            }

            .report-container {
                box-shadow: none;
                margin: 0;
                max-width: 100%;
            }

            .report-table th,
            .report-table td {
                font-size: 11px;
                padding: 6px;
            }

            /* فصل الصفحات عند الطباعة */
            .page-break {
                page-break-before: always;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                مجمع سيد الشهداء (عليه السلام) الخدمي
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-1"></i>التقارير المفصلة
                </a>
            </div>
        </div>
    </nav>

    <!-- فلاتر التقرير -->
    <div class="container mt-4 no-print">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>تقرير الموظفين - نموذج مبسط</h5>
                    <button class="btn btn-success" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة التقرير
                    </button>
                    <button class="btn btn-info ms-2" onclick="scrollToPage2()">
                        <i class="fas fa-chart-bar me-2"></i>عرض الإحصائيات
                    </button>
                </div>
                
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="report_date" class="form-label">تاريخ التقرير</label>
                        <input type="date" class="form-control" id="report_date" name="report_date" value="<?php echo str_replace('/', '-', $report_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="department" class="form-label">الشعبة</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">جميع الشعب</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo htmlspecialchars($dept['department']); ?>" 
                                        <?php echo $department == $dept['department'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($dept['department']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>إنشاء التقرير
                            </button>
                            <a href="simple_report.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- التقرير -->
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <div class="report-title">مجمع سيد الشهداء (عليه السلام) الخدمي</div>
        </div>
        
        <!-- معلومات التقرير -->
        <div class="report-info">
            <div><?php echo $report_date; ?></div>
            <div>الأحد</div>
            <div>موقف الإجازات الاعتيادية من الاستحقاق السنوي ليوم</div>
        </div>
        
        <!-- الجدول -->
        <table class="report-table">
            <thead>
                <tr>
                    <th rowspan="2" class="id-column">ID</th>
                    <th rowspan="2" class="name-column">الإجازات الاعتيادية<br>الاسم الرباعي واللقب</th>
                    <th rowspan="2" class="id-column">ID</th>
                    <th rowspan="2" class="name-column">الإجازات الاعتيادية<br>الاسم الرباعي واللقب</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $employeeChunks = array_chunk($employees, 2);
                foreach ($employeeChunks as $chunk): 
                ?>
                    <tr>
                        <!-- العمود الأول -->
                        <td><?php echo isset($chunk[0]) ? htmlspecialchars($chunk[0]['employee_id']) : ''; ?></td>
                        <td style="text-align: right; padding-right: 10px;">
                            <?php echo isset($chunk[0]) ? htmlspecialchars($chunk[0]['name']) : ''; ?>
                        </td>
                        
                        <!-- العمود الثاني -->
                        <td><?php echo isset($chunk[1]) ? htmlspecialchars($chunk[1]['employee_id']) : ''; ?></td>
                        <td style="text-align: right; padding-right: 10px;">
                            <?php echo isset($chunk[1]) ? htmlspecialchars($chunk[1]['name']) : ''; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
                
                <!-- إضافة صفوف فارغة إذا لزم الأمر -->
                <?php for ($i = count($employeeChunks); $i < 15; $i++): ?>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                <?php endfor; ?>
            </tbody>
        </table>
    </div>

    <!-- الصفحة الثانية - الإحصائيات -->
    <div class="report-container page-break" id="statistics-page" style="margin-top: 50px;">
        <!-- رأس التقرير -->
        <div class="report-header">
            <div class="report-title">مجمع سيد الشهداء (عليه السلام) الخدمي</div>
        </div>

        <!-- معلومات التقرير -->
        <div class="report-info">
            <div><?php echo $report_date; ?></div>
            <div>الأحد</div>
            <div>إحصائيات الحضور والإجازات</div>
        </div>

        <!-- جدول الإحصائيات -->
        <table class="report-table">
            <thead>
                <tr>
                    <th colspan="8" style="background-color: #f0f0f0; font-weight: bold; text-align: center;">
                        إحصائيات الحضور والإجازات
                    </th>
                </tr>
                <tr>
                    <th>العدد الكلي للموظفين</th>
                    <th>الحضور الفعلي</th>
                    <th>الغياب</th>
                    <th>المستضافين</th>
                    <th>الإجازات براتب</th>
                    <th>الإجازات بدون راتب</th>
                    <th>الإيفاد</th>
                    <th>الأجور اليومية</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="font-weight: bold; background-color: #f8f9fa;">
                        <?php echo $stats['total_employees']; ?>
                    </td>
                    <td style="background-color: #d4edda;">
                        <?php echo $stats['present_count']; ?>
                    </td>
                    <td style="background-color: #f8d7da;">
                        <?php echo $stats['absent_count']; ?>
                    </td>
                    <td style="background-color: #d1ecf1;">
                        <?php echo $stats['usage_count']; ?>
                    </td>
                    <td style="background-color: #d4edda;">
                        <?php echo $stats['annual_leave_count']; ?>
                    </td>
                    <td style="background-color: #fff3cd;">
                        <?php echo $stats['unpaid_leave_count']; ?>
                    </td>
                    <td style="background-color: #e2e3e5;">
                        <?php echo $stats['usage_count']; ?>
                    </td>
                    <td style="background-color: #f8f9fa;">
                        -
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- جدول الحقول التفصيلية -->
        <table class="report-table" style="margin-top: 30px;">
            <thead>
                <tr>
                    <th colspan="6" style="background-color: #f0f0f0; font-weight: bold; text-align: center;">
                        تفاصيل الحقول
                    </th>
                </tr>
                <tr>
                    <th>العدد الكلي</th>
                    <th>الحضور الفعلي</th>
                    <th>براتب</th>
                    <th>بدون راتب</th>
                    <th>الغياب</th>
                    <th>المستضافين</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td style="font-weight: bold; text-align: center;">
                        <?php echo $stats['total_employees']; ?>
                    </td>
                    <td style="text-align: center;">
                        <?php echo $stats['present_count']; ?>
                    </td>
                    <td style="text-align: center;">
                        <?php echo $stats['annual_leave_count']; ?>
                    </td>
                    <td style="text-align: center;">
                        <?php echo $stats['unpaid_leave_count']; ?>
                    </td>
                    <td style="text-align: center;">
                        <?php echo $stats['absent_count']; ?>
                    </td>
                    <td style="text-align: center;">
                        <?php echo $stats['usage_count']; ?>
                    </td>
                </tr>
                <!-- صفوف فارغة للتوقيعات -->
                <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
                <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
                <tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr>
            </tbody>
        </table>

        <!-- التوقيعات -->
        <div style="margin-top: 50px; text-align: center;">
            <div style="margin-bottom: 30px;">
                <div style="font-weight: bold; font-size: 16px; margin-bottom: 10px;">
                    الحاج مصطفى عباس خضر
                </div>
                <div style="font-size: 14px; border-top: 1px solid #000; width: 200px; margin: 0 auto; padding-top: 5px;">
                    رئيس القسم
                </div>
            </div>

            <div style="margin-top: 40px;">
                <div style="font-weight: bold; font-size: 16px; margin-bottom: 10px;">
                    مدير مجمع سيد الشهداء (عليه السلام) الخدمي
                </div>
                <div style="font-size: 14px; border-top: 1px solid #000; width: 250px; margin: 0 auto; padding-top: 5px;">
                    المدير العام
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function scrollToPage2() {
            document.getElementById('statistics-page').scrollIntoView({
                behavior: 'smooth'
            });
        }
    </script>
</body>
</html>
