<?php
require_once 'config.php';
checkLogin();

// معالجة الفلاتر
$report_date = isset($_GET['report_date']) ? $_GET['report_date'] : date('Y/m/d');
$department = isset($_GET['department']) ? $_GET['department'] : '';

// جلب الموظفين
$whereConditions = [];
$params = [];

if ($_SESSION['role'] != 'admin') {
    $whereConditions[] = "user_id = ?";
    $params[] = $_SESSION['user_id'];
}

if ($department) {
    $whereConditions[] = "department = ?";
    $params[] = $department;
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

$stmt = $pdo->prepare("
    SELECT employee_id, name, department 
    FROM employees 
    {$whereClause}
    ORDER BY employee_id
");
$stmt->execute($params);
$employees = $stmt->fetchAll();

// جلب الأقسام للفلترة
if ($_SESSION['role'] == 'admin') {
    $deptStmt = $pdo->query("SELECT DISTINCT department FROM employees ORDER BY department");
} else {
    $deptStmt = $pdo->prepare("SELECT DISTINCT department FROM employees WHERE user_id = ? ORDER BY department");
    $deptStmt->execute([$_SESSION['user_id']]);
}
$departments = $deptStmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير الموظفين - نموذج مبسط</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        
        .report-container {
            background: white;
            margin: 20px auto;
            max-width: 800px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .report-header {
            text-align: center;
            padding: 20px;
            border-bottom: 2px solid #000;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            border-bottom: 1px solid #000;
            font-size: 14px;
        }
        
        .report-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .report-table th,
        .report-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            font-size: 12px;
        }
        
        .report-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        
        .id-column {
            width: 80px;
        }
        
        .name-column {
            width: 200px;
        }
        
        @media print {
            @page {
                size: A4;
                margin: 1cm;
            }
            
            body {
                background: white;
            }
            
            .no-print {
                display: none !important;
            }
            
            .report-container {
                box-shadow: none;
                margin: 0;
                max-width: 100%;
            }
            
            .report-table th,
            .report-table td {
                font-size: 11px;
                padding: 6px;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                نظام إدارة الموظفين
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-1"></i>التقارير المفصلة
                </a>
            </div>
        </div>
    </nav>

    <!-- فلاتر التقرير -->
    <div class="container mt-4 no-print">
        <div class="card mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5>تقرير الموظفين - نموذج مبسط</h5>
                    <button class="btn btn-success" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة التقرير
                    </button>
                </div>
                
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="report_date" class="form-label">تاريخ التقرير</label>
                        <input type="date" class="form-control" id="report_date" name="report_date" value="<?php echo str_replace('/', '-', $report_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="department" class="form-label">الشعبة</label>
                        <select class="form-select" id="department" name="department">
                            <option value="">جميع الشعب</option>
                            <?php foreach ($departments as $dept): ?>
                                <option value="<?php echo htmlspecialchars($dept['department']); ?>" 
                                        <?php echo $department == $dept['department'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($dept['department']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i>إنشاء التقرير
                            </button>
                            <a href="simple_report.php" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- التقرير -->
    <div class="report-container">
        <!-- رأس التقرير -->
        <div class="report-header">
            <div class="report-title">مجمع سيد الشهداء (ع) الثقافي</div>
        </div>
        
        <!-- معلومات التقرير -->
        <div class="report-info">
            <div><?php echo $report_date; ?></div>
            <div>الأحد</div>
            <div>موقف الإجازات الاعتيادية من الاستحقاق السنوي ليوم</div>
        </div>
        
        <!-- الجدول -->
        <table class="report-table">
            <thead>
                <tr>
                    <th rowspan="2" class="id-column">ID</th>
                    <th rowspan="2" class="name-column">الإجازات الاعتيادية<br>الاسم الرباعي واللقب</th>
                    <th rowspan="2" class="id-column">ID</th>
                    <th rowspan="2" class="name-column">الإجازات الاعتيادية<br>الاسم الرباعي واللقب</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $employeeChunks = array_chunk($employees, 2);
                foreach ($employeeChunks as $chunk): 
                ?>
                    <tr>
                        <!-- العمود الأول -->
                        <td><?php echo isset($chunk[0]) ? htmlspecialchars($chunk[0]['employee_id']) : ''; ?></td>
                        <td style="text-align: right; padding-right: 10px;">
                            <?php echo isset($chunk[0]) ? htmlspecialchars($chunk[0]['name']) : ''; ?>
                        </td>
                        
                        <!-- العمود الثاني -->
                        <td><?php echo isset($chunk[1]) ? htmlspecialchars($chunk[1]['employee_id']) : ''; ?></td>
                        <td style="text-align: right; padding-right: 10px;">
                            <?php echo isset($chunk[1]) ? htmlspecialchars($chunk[1]['name']) : ''; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
                
                <!-- إضافة صفوف فارغة إذا لزم الأمر -->
                <?php for ($i = count($employeeChunks); $i < 15; $i++): ?>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                <?php endfor; ?>
            </tbody>
        </table>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
