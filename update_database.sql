-- تحديث قاعدة البيانات للنسخة الجديدة
-- تشغيل هذا الملف إذا كان لديك النسخة القديمة من النظام

USE employees_db;

-- تحديث جدول الموظفين
-- تغيير نوع الرصيد من DECIMAL إلى INT
ALTER TABLE employees MODIFY COLUMN balance INT DEFAULT 0 COMMENT 'الرصيد بالأيام';

-- إضافة تعليق للشعبة
ALTER TABLE employees MODIFY COLUMN department VARCHAR(100) NOT NULL COMMENT 'الشعبة';

-- تغ<PERSON><PERSON>ر اسم العمود من job_number إلى employee_id
ALTER TABLE employees CHANGE COLUMN job_number employee_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'معرف الموظف';

-- تحديث جدول الحضور والغياب
-- إضافة الحالات الجديدة
ALTER TABLE attendance MODIFY COLUMN status ENUM('حاضر', 'غائب', 'سنوية', 'بدون راتب', 'استخدام') NOT NULL;

-- تحديث البيانات الموجودة (إذا كانت هناك سجلات بحالة "إجازة")
UPDATE attendance SET status = 'سنوية' WHERE status = 'إجازة';

-- إضافة فهرس لتحسين الأداء في التقارير
CREATE INDEX idx_attendance_date ON attendance(date);
CREATE INDEX idx_attendance_status ON attendance(status);
CREATE INDEX idx_employees_department ON employees(department);

-- تحديث الرصيد للموظفين الموجودين (تحويل من عملة إلى أيام)
-- هذا مثال - يمكن تعديله حسب الحاجة
UPDATE employees SET balance = FLOOR(balance) WHERE balance > 0;

COMMIT;
