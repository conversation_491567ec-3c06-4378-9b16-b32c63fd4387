<?php
require_once 'config.php';
checkLogin();

$message = '';
$messageType = 'success';

// معالجة الفلاتر
$report_type = isset($_GET['type']) ? $_GET['type'] : 'daily';
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');
$employee_id = isset($_GET['employee_id']) ? intval($_GET['employee_id']) : '';

// تحديد التواريخ حسب نوع التقرير
switch ($report_type) {
    case 'daily':
        $start_date = $end_date = date('Y-m-d');
        break;
    case 'weekly':
        $start_date = date('Y-m-d', strtotime('monday this week'));
        $end_date = date('Y-m-d', strtotime('sunday this week'));
        break;
    case 'monthly':
        $start_date = date('Y-m-01');
        $end_date = date('Y-m-t');
        break;
    case 'yearly':
        $start_date = date('Y-01-01');
        $end_date = date('Y-12-31');
        break;
    case 'custom':
        // استخدام التواريخ المحددة من المستخدم
        break;
}

// جلب الموظفين للمستخدم الحالي
if ($_SESSION['role'] == 'admin') {
    $employeesStmt = $pdo->query("SELECT id, name, employee_id, department FROM employees ORDER BY name");
} else {
    $employeesStmt = $pdo->prepare("SELECT id, name, employee_id, department FROM employees WHERE user_id = ? ORDER BY name");
    $employeesStmt->execute([$_SESSION['user_id']]);
}
$employees = $employeesStmt->fetchAll();

// بناء الاستعلام
$whereConditions = ["a.date BETWEEN ? AND ?"];
$params = [$start_date, $end_date];

if ($_SESSION['role'] != 'admin') {
    $whereConditions[] = "e.user_id = ?";
    $params[] = $_SESSION['user_id'];
}

if ($employee_id) {
    $whereConditions[] = "a.employee_id = ?";
    $params[] = $employee_id;
}

$whereClause = "WHERE " . implode(" AND ", $whereConditions);

// جلب بيانات التقرير
$stmt = $pdo->prepare("
    SELECT
        e.name as employee_name,
        e.employee_id,
        e.department,
        a.date,
        a.status,
        COUNT(CASE WHEN a.status = 'حاضر' THEN 1 END) as present_days,
        COUNT(CASE WHEN a.status = 'غائب' THEN 1 END) as absent_days,
        COUNT(CASE WHEN a.status = 'سنوية' THEN 1 END) as annual_days,
        COUNT(CASE WHEN a.status = 'بدون راتب' THEN 1 END) as unpaid_days,
        COUNT(CASE WHEN a.status = 'استخدام' THEN 1 END) as usage_days,
        COUNT(CASE WHEN a.status = 'وفاة' THEN 1 END) as death_days
    FROM attendance a
    JOIN employees e ON a.employee_id = e.id
    {$whereClause}
    GROUP BY e.id, e.name, e.employee_id, e.department
    ORDER BY e.name
");
$stmt->execute($params);
$reportData = $stmt->fetchAll();

// جلب التفاصيل اليومية
$detailsStmt = $pdo->prepare("
    SELECT
        e.name as employee_name,
        e.employee_id,
        e.department,
        a.date,
        a.status
    FROM attendance a
    JOIN employees e ON a.employee_id = e.id
    {$whereClause}
    ORDER BY a.date DESC, e.name
");
$detailsStmt->execute($params);
$detailsData = $detailsStmt->fetchAll();

// حساب الإحصائيات العامة
$totalStats = [
    'total_employees' => count($reportData),
    'total_present' => array_sum(array_column($reportData, 'present_days')),
    'total_absent' => array_sum(array_column($reportData, 'absent_days')),
    'total_annual' => array_sum(array_column($reportData, 'annual_days')),
    'total_unpaid' => array_sum(array_column($reportData, 'unpaid_days')),
    'total_usage' => array_sum(array_column($reportData, 'usage_days')),
    'total_death' => array_sum(array_column($reportData, 'death_days'))
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        @media print {
            @page {
                size: A4;
                margin: 1cm;
            }

            body {
                font-size: 12px;
                line-height: 1.4;
                color: #000;
                background: white;
            }

            .no-print {
                display: none !important;
            }

            .card {
                box-shadow: none;
                border: 1px solid #333;
                margin-bottom: 20px;
                page-break-inside: avoid;
            }

            .card-header {
                background-color: #f8f9fa !important;
                border-bottom: 2px solid #333;
                font-weight: bold;
            }

            .table {
                font-size: 11px;
            }

            .table th {
                background-color: #f8f9fa !important;
                border: 1px solid #333 !important;
                font-weight: bold;
                text-align: center;
            }

            .table td {
                border: 1px solid #333 !important;
                text-align: center;
            }

            .badge {
                background-color: #6c757d !important;
                color: white !important;
                border: 1px solid #333;
            }

            .stat-card {
                border: 1px solid #333;
                margin-bottom: 10px;
            }

            .stat-number {
                font-size: 1.5rem;
                color: #000 !important;
            }

            h2, h3, h5 {
                color: #000 !important;
            }

            .container {
                max-width: 100% !important;
                padding: 0 !important;
            }

            .row {
                margin: 0 !important;
            }

            .col-md-2 {
                width: 16.66% !important;
                float: left;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary no-print">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                نظام إدارة الموظفين
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="simple_report.php">
                    <i class="fas fa-file-alt me-1"></i>التقرير المبسط
                </a>
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4 no-print">
                    <h2><i class="fas fa-chart-bar me-2"></i>التقارير</h2>
                    <button class="btn btn-success" onclick="window.print()">
                        <i class="fas fa-print me-2"></i>طباعة التقرير
                    </button>
                </div>

                <?php if ($message): ?>
                    <?php echo showMessage($message, $messageType); ?>
                <?php endif; ?>

                <!-- فلاتر التقرير -->
                <div class="card mb-4 no-print">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="type" class="form-label">نوع التقرير</label>
                                <select class="form-select" id="type" name="type" onchange="toggleCustomDates()">
                                    <option value="daily" <?php echo $report_type == 'daily' ? 'selected' : ''; ?>>يومي</option>
                                    <option value="weekly" <?php echo $report_type == 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                    <option value="monthly" <?php echo $report_type == 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                    <option value="yearly" <?php echo $report_type == 'yearly' ? 'selected' : ''; ?>>سنوي</option>
                                    <option value="custom" <?php echo $report_type == 'custom' ? 'selected' : ''; ?>>مخصص</option>
                                </select>
                            </div>
                            <div class="col-md-3" id="custom-dates" style="<?php echo $report_type != 'custom' ? 'display:none;' : ''; ?>">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $start_date; ?>">
                            </div>
                            <div class="col-md-3" id="custom-dates-end" style="<?php echo $report_type != 'custom' ? 'display:none;' : ''; ?>">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $end_date; ?>">
                            </div>
                            <div class="col-md-3">
                                <label for="employee_id" class="form-label">الموظف</label>
                                <select class="form-select" id="employee_id" name="employee_id">
                                    <option value="">جميع الموظفين</option>
                                    <?php foreach ($employees as $employee): ?>
                                        <option value="<?php echo $employee['id']; ?>" 
                                                <?php echo $employee_id == $employee['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($employee['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i>إنشاء التقرير
                                </button>
                                <a href="reports.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i>إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- عنوان التقرير -->
                <div class="text-center mb-4">
                    <h3>تقرير الحضور والغياب</h3>
                    <p class="text-muted">
                        <?php
                        $reportTitles = [
                            'daily' => 'تقرير يومي',
                            'weekly' => 'تقرير أسبوعي',
                            'monthly' => 'تقرير شهري',
                            'yearly' => 'تقرير سنوي',
                            'custom' => 'تقرير مخصص'
                        ];
                        echo $reportTitles[$report_type];
                        ?>
                        - من <?php echo $start_date; ?> إلى <?php echo $end_date; ?>
                    </p>
                </div>

                <!-- الإحصائيات العامة -->
                <div class="row mb-4">
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-primary"><?php echo $totalStats['total_employees']; ?></div>
                            <div class="text-muted">إجمالي الموظفين</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-success"><?php echo $totalStats['total_present']; ?></div>
                            <div class="text-muted">حاضر</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-danger"><?php echo $totalStats['total_absent']; ?></div>
                            <div class="text-muted">غائب</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-info"><?php echo $totalStats['total_annual']; ?></div>
                            <div class="text-muted">سنوية</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-warning"><?php echo $totalStats['total_unpaid']; ?></div>
                            <div class="text-muted">بدون راتب</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-secondary"><?php echo $totalStats['total_usage']; ?></div>
                            <div class="text-muted">استخدام</div>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="stat-card">
                            <div class="stat-number text-dark"><?php echo $totalStats['total_death']; ?></div>
                            <div class="text-muted">وفاة</div>
                        </div>
                    </div>
                </div>

                <!-- تقرير ملخص الموظفين -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>ملخص الحضور حسب الموظف</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($reportData)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد بيانات للفترة المحددة</h5>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>معرف الموظف</th>
                                            <th>الشعبة</th>
                                            <th>حاضر</th>
                                            <th>غائب</th>
                                            <th>سنوية</th>
                                            <th>بدون راتب</th>
                                            <th>استخدام</th>
                                            <th>وفاة</th>
                                            <th>المجموع</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reportData as $row): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($row['employee_name']); ?></td>
                                                <td><?php echo htmlspecialchars($row['employee_id']); ?></td>
                                                <td><?php echo htmlspecialchars($row['department']); ?></td>
                                                <td><span class="badge bg-success"><?php echo $row['present_days']; ?></span></td>
                                                <td><span class="badge bg-danger"><?php echo $row['absent_days']; ?></span></td>
                                                <td><span class="badge bg-info"><?php echo $row['annual_days']; ?></span></td>
                                                <td><span class="badge bg-warning text-dark"><?php echo $row['unpaid_days']; ?></span></td>
                                                <td><span class="badge bg-secondary"><?php echo $row['usage_days']; ?></span></td>
                                                <td><span class="badge bg-dark"><?php echo $row['death_days']; ?></span></td>
                                                <td><strong><?php echo ($row['present_days'] + $row['absent_days'] + $row['annual_days'] + $row['unpaid_days'] + $row['usage_days'] + $row['death_days']); ?></strong></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- التفاصيل اليومية -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-calendar-alt me-2"></i>التفاصيل اليومية</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($detailsData)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد سجلات للفترة المحددة</h5>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>التاريخ</th>
                                            <th>الموظف</th>
                                            <th>معرف الموظف</th>
                                            <th>الشعبة</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($detailsData as $detail): ?>
                                            <tr>
                                                <td><?php echo date('Y-m-d', strtotime($detail['date'])); ?></td>
                                                <td><?php echo htmlspecialchars($detail['employee_name']); ?></td>
                                                <td><?php echo htmlspecialchars($detail['employee_id']); ?></td>
                                                <td><?php echo htmlspecialchars($detail['department']); ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusIcon = '';
                                                    switch ($detail['status']) {
                                                        case 'حاضر':
                                                            $statusClass = 'bg-success';
                                                            $statusIcon = 'fas fa-check';
                                                            break;
                                                        case 'غائب':
                                                            $statusClass = 'bg-danger';
                                                            $statusIcon = 'fas fa-times';
                                                            break;
                                                        case 'سنوية':
                                                            $statusClass = 'bg-info';
                                                            $statusIcon = 'fas fa-umbrella-beach';
                                                            break;
                                                        case 'بدون راتب':
                                                            $statusClass = 'bg-warning text-dark';
                                                            $statusIcon = 'fas fa-ban';
                                                            break;
                                                        case 'استخدام':
                                                            $statusClass = 'bg-secondary';
                                                            $statusIcon = 'fas fa-briefcase';
                                                            break;
                                                        case 'وفاة':
                                                            $statusClass = 'bg-dark';
                                                            $statusIcon = 'fas fa-cross';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $statusClass; ?>">
                                                        <i class="<?php echo $statusIcon; ?> me-1"></i>
                                                        <?php echo $detail['status']; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function toggleCustomDates() {
            const type = document.getElementById('type').value;
            const customDates = document.getElementById('custom-dates');
            const customDatesEnd = document.getElementById('custom-dates-end');

            if (type === 'custom') {
                customDates.style.display = 'block';
                customDatesEnd.style.display = 'block';
            } else {
                customDates.style.display = 'none';
                customDatesEnd.style.display = 'none';
            }
        }
    </script>
</body>
</html>
