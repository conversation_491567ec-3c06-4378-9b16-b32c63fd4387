<?php
require_once 'config.php';
checkLogin();

// إحصائيات للمستخدم الحالي
$stats = [];

if ($_SESSION['role'] == 'admin') {
    // إحصائيات المدير - جميع البيانات
    $stmt = $pdo->query("SELECT COUNT(*) as total_employees FROM employees");
    $stats['total_employees'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users WHERE role = 'user'");
    $stats['total_users'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as today_attendance FROM attendance WHERE date = CURDATE()");
    $stats['today_attendance'] = $stmt->fetchColumn();
} else {
    // إحصائيات المستخدم العادي - بياناته فقط
    $stmt = $pdo->prepare("SELECT COUNT(*) as my_employees FROM employees WHERE user_id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['my_employees'] = $stmt->fetchColumn();
    
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as today_attendance 
        FROM attendance a 
        JOIN employees e ON a.employee_id = e.id 
        WHERE e.user_id = ? AND a.date = CURDATE()
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $stats['today_attendance'] = $stmt->fetchColumn();
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - مجمع سيد الشهداء (عليه السلام) الخدمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: 700;
        }
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        .menu-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-decoration: none;
            color: inherit;
            transition: all 0.3s;
            display: block;
        }
        .menu-card:hover {
            transform: translateY(-5px);
            color: inherit;
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                مجمع سيد الشهداء (عليه السلام) الخدمي
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i>
                        <?php echo $_SESSION['username']; ?>
                        <?php if ($_SESSION['role'] == 'admin'): ?>
                            <span class="badge bg-warning text-dark ms-1">مدير</span>
                        <?php endif; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- ترحيب -->
        <div class="row mb-4">
            <div class="col-12">
                <h2 class="text-primary">
                    مرحباً، <?php echo $_SESSION['username']; ?>
                    <?php if ($_SESSION['role'] == 'admin'): ?>
                        <small class="text-muted">(مدير النظام)</small>
                    <?php endif; ?>
                </h2>
            </div>
        </div>

        <!-- الإحصائيات -->
        <div class="row mb-4">
            <?php if ($_SESSION['role'] == 'admin'): ?>
                <div class="col-md-4 mb-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo $stats['total_employees']; ?></h3>
                                <p class="text-muted mb-0">إجمالي الموظفين</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-success">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo $stats['total_users']; ?></h3>
                                <p class="text-muted mb-0">المستخدمين</p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="col-md-6 mb-3">
                    <div class="stat-card">
                        <div class="d-flex align-items-center">
                            <div class="stat-icon bg-primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="ms-3">
                                <h3 class="mb-0"><?php echo $stats['my_employees']; ?></h3>
                                <p class="text-muted mb-0">موظفيني</p>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="col-md-<?php echo $_SESSION['role'] == 'admin' ? '4' : '6'; ?> mb-3">
                <div class="stat-card">
                    <div class="d-flex align-items-center">
                        <div class="stat-icon bg-info">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="mb-0"><?php echo $stats['today_attendance']; ?></h3>
                            <p class="text-muted mb-0">حضور اليوم</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- القوائم -->
        <div class="row">
            <div class="col-md-4 mb-3">
                <a href="employees.php" class="menu-card">
                    <div class="text-center">
                        <i class="fas fa-users fa-3x text-primary mb-3"></i>
                        <h5>إدارة الموظفين</h5>
                        <p class="text-muted">إضافة وتعديل وحذف الموظفين</p>
                    </div>
                </a>
            </div>
            
            <div class="col-md-4 mb-3">
                <a href="attendance.php" class="menu-card">
                    <div class="text-center">
                        <i class="fas fa-calendar-alt fa-3x text-success mb-3"></i>
                        <h5>سجلات الحضور</h5>
                        <p class="text-muted">إدارة حضور وغياب الموظفين</p>
                    </div>
                </a>
            </div>

            <div class="col-md-4 mb-3">
                <a href="reports.php" class="menu-card">
                    <div class="text-center">
                        <i class="fas fa-chart-bar fa-3x text-info mb-3"></i>
                        <h5>التقارير المفصلة</h5>
                        <p class="text-muted">تقارير يومية وأسبوعية وشهرية</p>
                    </div>
                </a>
            </div>

            <div class="col-md-4 mb-3">
                <a href="simple_report.php" class="menu-card">
                    <div class="text-center">
                        <i class="fas fa-file-alt fa-3x text-secondary mb-3"></i>
                        <h5>تقرير الموظفين</h5>
                        <p class="text-muted">تقرير مبسط بتصميم كلاسيكي</p>
                    </div>
                </a>
            </div>

            <?php if ($_SESSION['role'] == 'admin'): ?>
            <div class="col-md-4 mb-3">
                <a href="users.php" class="menu-card">
                    <div class="text-center">
                        <i class="fas fa-user-cog fa-3x text-warning mb-3"></i>
                        <h5>إدارة المستخدمين</h5>
                        <p class="text-muted">إضافة وإدارة مستخدمي النظام</p>
                    </div>
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
