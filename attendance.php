<?php
require_once 'config.php';
checkLogin();

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $employee_id = intval($_POST['employee_id']);
                $date = $_POST['date'];
                $status = $_POST['status'];
                
                if (empty($employee_id) || empty($date) || empty($status)) {
                    $message = 'جميع الحقول مطلوبة';
                    $messageType = 'danger';
                } else {
                    try {
                        // التحقق من ملكية الموظف
                        $checkStmt = $pdo->prepare("SELECT id FROM employees WHERE id = ? AND (user_id = ? OR ? = 'admin')");
                        $checkStmt->execute([$employee_id, $_SESSION['user_id'], $_SESSION['role']]);
                        
                        if ($checkStmt->fetch()) {
                            $stmt = $pdo->prepare("INSERT INTO attendance (employee_id, date, status) VALUES (?, ?, ?)");
                            $stmt->execute([$employee_id, $date, $status]);
                            $message = 'تم إضافة سجل الحضور بنجاح';
                        } else {
                            $message = 'غير مسموح لك بإضافة سجل لهذا الموظف';
                            $messageType = 'danger';
                        }
                    } catch(PDOException $e) {
                        if ($e->getCode() == 23000) {
                            $message = 'يوجد سجل حضور لهذا الموظف في هذا التاريخ مسبقاً';
                            $messageType = 'danger';
                        } else {
                            $message = 'خطأ في إضافة سجل الحضور';
                            $messageType = 'danger';
                        }
                    }
                }
                break;
                
            case 'edit':
                $id = intval($_POST['attendance_id']);
                $status = $_POST['status'];
                
                if (empty($status)) {
                    $message = 'حالة الحضور مطلوبة';
                    $messageType = 'danger';
                } else {
                    try {
                        // التحقق من ملكية السجل
                        $checkStmt = $pdo->prepare("
                            SELECT a.id 
                            FROM attendance a 
                            JOIN employees e ON a.employee_id = e.id 
                            WHERE a.id = ? AND (e.user_id = ? OR ? = 'admin')
                        ");
                        $checkStmt->execute([$id, $_SESSION['user_id'], $_SESSION['role']]);
                        
                        if ($checkStmt->fetch()) {
                            $stmt = $pdo->prepare("UPDATE attendance SET status = ? WHERE id = ?");
                            $stmt->execute([$status, $id]);
                            $message = 'تم تحديث سجل الحضور بنجاح';
                        } else {
                            $message = 'غير مسموح لك بتعديل هذا السجل';
                            $messageType = 'danger';
                        }
                    } catch(PDOException $e) {
                        $message = 'خطأ في تحديث سجل الحضور';
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'delete':
                $id = intval($_POST['attendance_id']);
                
                try {
                    // التحقق من ملكية السجل
                    $checkStmt = $pdo->prepare("
                        SELECT a.id 
                        FROM attendance a 
                        JOIN employees e ON a.employee_id = e.id 
                        WHERE a.id = ? AND (e.user_id = ? OR ? = 'admin')
                    ");
                    $checkStmt->execute([$id, $_SESSION['user_id'], $_SESSION['role']]);
                    
                    if ($checkStmt->fetch()) {
                        $stmt = $pdo->prepare("DELETE FROM attendance WHERE id = ?");
                        $stmt->execute([$id]);
                        $message = 'تم حذف سجل الحضور بنجاح';
                    } else {
                        $message = 'غير مسموح لك بحذف هذا السجل';
                        $messageType = 'danger';
                    }
                } catch(PDOException $e) {
                    $message = 'خطأ في حذف سجل الحضور';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// جلب الموظفين للمستخدم الحالي
if ($_SESSION['role'] == 'admin') {
    $employeesStmt = $pdo->query("SELECT id, name FROM employees ORDER BY name");
} else {
    $employeesStmt = $pdo->prepare("SELECT id, name FROM employees WHERE user_id = ? ORDER BY name");
    $employeesStmt->execute([$_SESSION['user_id']]);
}
$employees = $employeesStmt->fetchAll();

// جلب سجلات الحضور
$filter_employee = isset($_GET['employee']) ? intval($_GET['employee']) : '';
$filter_date = isset($_GET['date']) ? $_GET['date'] : '';

$whereConditions = [];
$params = [];

if ($_SESSION['role'] != 'admin') {
    $whereConditions[] = "e.user_id = ?";
    $params[] = $_SESSION['user_id'];
}

if ($filter_employee) {
    $whereConditions[] = "a.employee_id = ?";
    $params[] = $filter_employee;
}

if ($filter_date) {
    $whereConditions[] = "a.date = ?";
    $params[] = $filter_date;
}

$whereClause = !empty($whereConditions) ? "WHERE " . implode(" AND ", $whereConditions) : "";

$stmt = $pdo->prepare("
    SELECT a.*, e.name as employee_name, e.employee_id
    FROM attendance a
    JOIN employees e ON a.employee_id = e.id
    {$whereClause}
    ORDER BY a.date DESC, e.name
");
$stmt->execute($params);
$attendanceRecords = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سجلات الحضور والغياب - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .status-badge {
            font-size: 0.85rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                نظام إدارة الموظفين
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees.php">
                    <i class="fas fa-users me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-1"></i>التقارير
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-calendar-alt me-2"></i>سجلات الحضور والغياب</h2>
                    <?php if (!empty($employees)): ?>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAttendanceModal">
                            <i class="fas fa-plus me-2"></i>إضافة سجل حضور
                        </button>
                    <?php endif; ?>
                </div>

                <?php if ($message): ?>
                    <?php echo showMessage($message, $messageType); ?>
                <?php endif; ?>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <label for="employee" class="form-label">الموظف</label>
                                <select class="form-select" id="employee" name="employee">
                                    <option value="">جميع الموظفين</option>
                                    <?php foreach ($employees as $employee): ?>
                                        <option value="<?php echo $employee['id']; ?>" 
                                                <?php echo $filter_employee == $employee['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($employee['name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="date" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="date" name="date" value="<?php echo $filter_date; ?>">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-search me-1"></i>بحث
                                    </button>
                                    <a href="attendance.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>إلغاء
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <?php if (empty($attendanceRecords)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد سجلات حضور</h5>
                                <?php if (empty($employees)): ?>
                                    <p class="text-muted">يجب إضافة موظفين أولاً</p>
                                    <a href="employees.php" class="btn btn-primary">إضافة موظف</a>
                                <?php else: ?>
                                    <p class="text-muted">ابدأ بإضافة سجل حضور جديد</p>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الموظف</th>
                                            <th>معرف الموظف</th>
                                            <th>التاريخ</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإضافة</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($attendanceRecords as $record): ?>
                                            <tr>
                                                <td>
                                                    <i class="fas fa-user me-2 text-primary"></i>
                                                    <?php echo htmlspecialchars($record['employee_name']); ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($record['employee_id']); ?></td>
                                                <td><?php echo date('Y-m-d', strtotime($record['date'])); ?></td>
                                                <td>
                                                    <?php
                                                    $statusClass = '';
                                                    $statusIcon = '';
                                                    switch ($record['status']) {
                                                        case 'حاضر':
                                                            $statusClass = 'bg-success';
                                                            $statusIcon = 'fas fa-check';
                                                            break;
                                                        case 'غائب':
                                                            $statusClass = 'bg-danger';
                                                            $statusIcon = 'fas fa-times';
                                                            break;
                                                        case 'سنوية':
                                                            $statusClass = 'bg-info';
                                                            $statusIcon = 'fas fa-umbrella-beach';
                                                            break;
                                                        case 'بدون راتب':
                                                            $statusClass = 'bg-warning text-dark';
                                                            $statusIcon = 'fas fa-ban';
                                                            break;
                                                        case 'استخدام':
                                                            $statusClass = 'bg-secondary';
                                                            $statusIcon = 'fas fa-briefcase';
                                                            break;
                                                    }
                                                    ?>
                                                    <span class="badge status-badge <?php echo $statusClass; ?>">
                                                        <i class="<?php echo $statusIcon; ?> me-1"></i>
                                                        <?php echo $record['status']; ?>
                                                    </span>
                                                </td>
                                                <td><?php echo date('Y-m-d H:i', strtotime($record['created_at'])); ?></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary me-1"
                                                            onclick="editAttendance(<?php echo $record['id']; ?>, '<?php echo $record['status']; ?>')">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger"
                                                            onclick="deleteAttendance(<?php echo $record['id']; ?>, '<?php echo htmlspecialchars($record['employee_name']); ?>', '<?php echo $record['date']; ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة سجل حضور -->
    <div class="modal fade" id="addAttendanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة سجل حضور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        <div class="mb-3">
                            <label for="employee_id" class="form-label">الموظف</label>
                            <select class="form-select" id="employee_id" name="employee_id" required>
                                <option value="">اختر الموظف</option>
                                <?php foreach ($employees as $employee): ?>
                                    <option value="<?php echo $employee['id']; ?>">
                                        <?php echo htmlspecialchars($employee['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="date" class="form-label">التاريخ</label>
                            <input type="date" class="form-control" id="date" name="date" value="<?php echo date('Y-m-d'); ?>" required>
                        </div>
                        <div class="mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">اختر الحالة</option>
                                <option value="حاضر">حاضر</option>
                                <option value="غائب">غائب</option>
                                <option value="سنوية">سنوية</option>
                                <option value="بدون راتب">بدون راتب</option>
                                <option value="استخدام">استخدام</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة السجل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج تعديل سجل حضور -->
    <div class="modal fade" id="editAttendanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل سجل الحضور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="attendance_id" id="edit_attendance_id">
                        <div class="mb-3">
                            <label for="edit_status" class="form-label">الحالة</label>
                            <select class="form-select" id="edit_status" name="status" required>
                                <option value="حاضر">حاضر</option>
                                <option value="غائب">غائب</option>
                                <option value="سنوية">سنوية</option>
                                <option value="بدون راتب">بدون راتب</option>
                                <option value="استخدام">استخدام</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج حذف سجل حضور -->
    <div class="modal fade" id="deleteAttendanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="attendance_id" id="delete_attendance_id">
                        <p>هل أنت متأكد من حذف سجل حضور الموظف <strong id="delete_employee_name"></strong> بتاريخ <strong id="delete_date"></strong>؟</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">حذف السجل</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editAttendance(id, status) {
            document.getElementById('edit_attendance_id').value = id;
            document.getElementById('edit_status').value = status;
            new bootstrap.Modal(document.getElementById('editAttendanceModal')).show();
        }

        function deleteAttendance(id, employeeName, date) {
            document.getElementById('delete_attendance_id').value = id;
            document.getElementById('delete_employee_name').textContent = employeeName;
            document.getElementById('delete_date').textContent = date;
            new bootstrap.Modal(document.getElementById('deleteAttendanceModal')).show();
        }
    </script>
</body>
</html>
