التحديثات الجديدة - نظام إدارة الموظفين
========================================

التحديثات المضافة:
==================

1. تحديث حالات الحضور والغياب:
   - حاضر
   - غائب  
   - سنوية (بدلاً من إجازة)
   - بدون راتب (جديد)
   - استخدام (جديد)

2. تغيير الرصيد:
   - الرصيد الآن بالأيام بدلاً من العملة
   - نوع البيانات: عدد صحيح بدلاً من عشري

3. تغيير التسميات:
   - "القسم" أصبح "الشعبة"
   - "رقم الوظيفة" أصبح "معرف الموظف"

4. إضافة نظام التقارير الشامل:
   - تقرير يومي
   - تقرير أسبوعي  
   - تقرير شهري
   - تقرير سنوي
   - تقرير مخصص (فترة محددة)

5. مميزات التقارير:
   - إحصائيات شاملة لكل نوع حضور
   - ملخص لكل موظف
   - تفاصيل يومية
   - إمكانية الفلترة حسب الموظف والتاريخ
   - طباعة محسنة بحجم A4 مع تنسيق احترافي
   - تصميم متجاوب للطباعة مع حدود وألوان محسنة

6. تحسينات واجهة المستخدم:
   - إضافة روابط التقارير في شريط التنقل
   - تحسين الألوان والأيقونات للحالات الجديدة
   - إضافة بطاقة التقارير في لوحة التحكم

الملفات الجديدة:
================
- reports.php: صفحة التقارير الشاملة
- update_database.sql: تحديث قاعدة البيانات للنسخة الجديدة
- UPDATES.txt: هذا الملف

الملفات المحدثة:
================
- database.sql: تحديث هيكل قاعدة البيانات
- employees.php: تحديث تسمية الشعبة والرصيد
- attendance.php: إضافة الحالات الجديدة
- dashboard.php: إضافة بطاقة التقارير
- users.php: تحديث شريط التنقل
- README.md: تحديث الوثائق
- INSTALL.txt: إضافة المميزات الجديدة

طريقة التحديث:
==============

للمستخدمين الجدد:
- استخدم ملف database.sql العادي

للمستخدمين الحاليين:
1. انسخ الملفات الجديدة والمحدثة
2. شغل ملف update_database.sql في phpMyAdmin
3. تأكد من تحديث جميع الملفات

التحديثات الأخيرة (الإصدار الجديد):
=====================================

7. تحديث معرف الموظف:
   - تغيير "رقم الوظيفة" إلى "معرف الموظف"
   - تحديث جميع الجداول والنماذج
   - تحديث التقارير لتعكس التغيير

8. تحسين طباعة التقارير:
   - تحسين تنسيق الطباعة لحجم A4
   - إضافة حدود للجداول في الطباعة
   - تحسين الألوان والخطوط للطباعة
   - تحسين تخطيط الصفحة للطباعة
   - إضافة فواصل الصفحات المناسبة

ملاحظات مهمة:
=============
- تأكد من عمل نسخة احتياطية من قاعدة البيانات قبل التحديث
- جميع البيانات الموجودة ستبقى محفوظة
- سجلات "إجازة" القديمة ستتحول تلقائياً إلى "سنوية"
- الرصيد سيتحول تلقائياً من عشري إلى صحيح
- "رقم الوظيفة" سيتحول تلقائياً إلى "معرف الموظف"
