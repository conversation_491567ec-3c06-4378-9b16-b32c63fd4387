<?php
require_once 'config.php';
checkLogin();

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $name = sanitize($_POST['name']);
                $employee_id = sanitize($_POST['employee_id']);
                $department = sanitize($_POST['department']);
                $balance = intval($_POST['balance']);
                
                if (empty($name) || empty($employee_id) || empty($department)) {
                    $message = 'جميع الحقول مطلوبة';
                    $messageType = 'danger';
                } else {
                    try {
                        $stmt = $pdo->prepare("INSERT INTO employees (name, employee_id, department, balance, user_id) VALUES (?, ?, ?, ?, ?)");
                        $stmt->execute([$name, $employee_id, $department, $balance, $_SESSION['user_id']]);
                        $message = 'تم إضافة الموظف بنجاح';
                    } catch(PDOException $e) {
                        if ($e->getCode() == 23000) {
                            $message = 'معرف الموظف موجود مسبقاً';
                            $messageType = 'danger';
                        } else {
                            $message = 'خطأ في إضافة الموظف';
                            $messageType = 'danger';
                        }
                    }
                }
                break;
                
            case 'edit':
                $id = intval($_POST['employee_id']);
                $name = sanitize($_POST['name']);
                $balance = intval($_POST['balance']);
                
                if (empty($name)) {
                    $message = 'اسم الموظف مطلوب';
                    $messageType = 'danger';
                } else {
                    try {
                        // التحقق من ملكية الموظف
                        $checkStmt = $pdo->prepare("SELECT id FROM employees WHERE id = ? AND (user_id = ? OR ? = 'admin')");
                        $checkStmt->execute([$id, $_SESSION['user_id'], $_SESSION['role']]);
                        
                        if ($checkStmt->fetch()) {
                            $stmt = $pdo->prepare("UPDATE employees SET name = ?, balance = ? WHERE id = ?");
                            $stmt->execute([$name, $balance, $id]);
                            $message = 'تم تحديث بيانات الموظف بنجاح';
                        } else {
                            $message = 'غير مسموح لك بتعديل هذا الموظف';
                            $messageType = 'danger';
                        }
                    } catch(PDOException $e) {
                        $message = 'خطأ في تحديث بيانات الموظف';
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'delete':
                $id = intval($_POST['employee_id']);
                
                try {
                    // التحقق من ملكية الموظف
                    $checkStmt = $pdo->prepare("SELECT id FROM employees WHERE id = ? AND (user_id = ? OR ? = 'admin')");
                    $checkStmt->execute([$id, $_SESSION['user_id'], $_SESSION['role']]);
                    
                    if ($checkStmt->fetch()) {
                        $stmt = $pdo->prepare("DELETE FROM employees WHERE id = ?");
                        $stmt->execute([$id]);
                        $message = 'تم حذف الموظف بنجاح';
                    } else {
                        $message = 'غير مسموح لك بحذف هذا الموظف';
                        $messageType = 'danger';
                    }
                } catch(PDOException $e) {
                    $message = 'خطأ في حذف الموظف';
                    $messageType = 'danger';
                }
                break;
        }
    }
}

// جلب الموظفين
if ($_SESSION['role'] == 'admin') {
    $stmt = $pdo->query("
        SELECT e.*, u.username 
        FROM employees e 
        JOIN users u ON e.user_id = u.id 
        ORDER BY e.created_at DESC
    ");
} else {
    $stmt = $pdo->prepare("
        SELECT e.*, u.username 
        FROM employees e 
        JOIN users u ON e.user_id = u.id 
        WHERE e.user_id = ? 
        ORDER BY e.created_at DESC
    ");
    $stmt->execute([$_SESSION['user_id']]);
}
$employees = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - مجمع سيد الشهداء (عليه السلام) الخدمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                مجمع سيد الشهداء (عليه السلام) الخدمي
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="attendance.php">
                    <i class="fas fa-calendar-alt me-1"></i>الحضور
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-1"></i>التقارير
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-users me-2"></i>إدارة الموظفين</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addEmployeeModal">
                        <i class="fas fa-plus me-2"></i>إضافة موظف جديد
                    </button>
                </div>

                <?php if ($message): ?>
                    <?php echo showMessage($message, $messageType); ?>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <?php if (empty($employees)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">لا توجد موظفين</h5>
                                <p class="text-muted">ابدأ بإضافة موظف جديد</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>معرف الموظف</th>
                                            <th>الشعبة</th>
                                            <th>الرصيد</th>
                                            <?php if ($_SESSION['role'] == 'admin'): ?>
                                                <th>المستخدم</th>
                                            <?php endif; ?>
                                            <th>تاريخ الإضافة</th>
                                            <th>العمليات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($employees as $employee): ?>
                                            <tr>
                                                <td>
                                                    <i class="fas fa-user me-2 text-primary"></i>
                                                    <?php echo htmlspecialchars($employee['name']); ?>
                                                </td>
                                                <td><?php echo htmlspecialchars($employee['employee_id']); ?></td>
                                                <td><?php echo htmlspecialchars($employee['department']); ?></td>
                                                <td>
                                                    <span class="badge bg-success">
                                                        <?php echo $employee['balance']; ?> يوم
                                                    </span>
                                                </td>
                                                <?php if ($_SESSION['role'] == 'admin'): ?>
                                                    <td><?php echo htmlspecialchars($employee['username']); ?></td>
                                                <?php endif; ?>
                                                <td><?php echo date('Y-m-d', strtotime($employee['created_at'])); ?></td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary me-1" 
                                                            onclick="editEmployee(<?php echo $employee['id']; ?>, '<?php echo htmlspecialchars($employee['name']); ?>', <?php echo $employee['balance']; ?>)">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteEmployee(<?php echo $employee['id']; ?>, '<?php echo htmlspecialchars($employee['name']); ?>')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة موظف -->
    <div class="modal fade" id="addEmployeeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        <div class="mb-3">
                            <label for="name" class="form-label">اسم الموظف</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="employee_id" class="form-label">معرف الموظف</label>
                            <input type="text" class="form-control" id="employee_id" name="employee_id" required>
                        </div>
                        <div class="mb-3">
                            <label for="department" class="form-label">الشعبة</label>
                            <input type="text" class="form-control" id="department" name="department" required>
                        </div>
                        <div class="mb-3">
                            <label for="balance" class="form-label">الرصيد (بالأيام)</label>
                            <input type="number" class="form-control" id="balance" name="balance" value="0">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة الموظف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج تعديل موظف -->
    <div class="modal fade" id="editEmployeeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل بيانات الموظف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="employee_id" id="edit_employee_id">
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">اسم الموظف</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_balance" class="form-label">الرصيد (بالأيام)</label>
                            <input type="number" class="form-control" id="edit_balance" name="balance">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج حذف موظف -->
    <div class="modal fade" id="deleteEmployeeModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="employee_id" id="delete_employee_id">
                        <p>هل أنت متأكد من حذف الموظف <strong id="delete_employee_name"></strong>؟</p>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            سيتم حذف جميع سجلات الحضور والغياب المرتبطة بهذا الموظف
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">حذف الموظف</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editEmployee(id, name, balance) {
            document.getElementById('edit_employee_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_balance').value = balance;
            new bootstrap.Modal(document.getElementById('editEmployeeModal')).show();
        }

        function deleteEmployee(id, name) {
            document.getElementById('delete_employee_id').value = id;
            document.getElementById('delete_employee_name').textContent = name;
            new bootstrap.Modal(document.getElementById('deleteEmployeeModal')).show();
        }
    </script>
</body>
</html>
