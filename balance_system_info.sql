-- معلومات نظام الرصيد التلقائي
-- هذا الملف يحتوي على معلومات حول كيفية عمل نظام استقطاع الرصيد

/*
نظام استقطاع الرصيد التلقائي:
==========================

1. عند إضافة سجل حضور بحالة "سنوية":
   - يتم التحقق من رصيد الموظف
   - إذا كان الرصيد > 0: يتم إضافة السجل واستقطاع يوم من الرصيد
   - إذا كان الرصيد <= 0: يتم رفض العملية مع رسالة خطأ

2. عند تعديل سجل حضور:
   - من "سنوية" إلى حالة أخرى: يتم إضافة يوم للرصيد
   - من حالة أخرى إلى "سنوية": يتم استقطاع يوم من الرصيد (مع التحقق من الرصيد)
   - من "سنوية" إلى "سنوية": لا يتغير الرصيد

3. عند حذف سجل حضور:
   - إذا كان السجل المحذوف بحالة "سنوية": يتم إعادة يوم للرصيد

4. الحماية:
   - استخدام المعاملات (Transactions) لضمان سلامة البيانات
   - التحقق من الرصيد قبل السماح بالإجازة السنوية
   - رسائل واضحة للمستخدم عن حالة الرصيد

5. واجهة المستخدم:
   - عرض الرصيد الحالي لكل موظف في قائمة الاختيار
   - تحذيرات عند اختيار موظف برصيد منخفض
   - تنبيه عند اختيار "إجازة سنوية" مع عرض تأثيرها على الرصيد

6. بوابة الموظف:
   - عرض الرصيد المتبقي الفعلي (بعد الاستقطاعات)
   - عرض عدد الإجازات المستخدمة في السنة الحالية
   - ملاحظات توضيحية حول نظام الاستقطاع

ملاحظات مهمة:
==============
- الرصيد في جدول employees يمثل الرصيد الحالي المتاح (وليس الرصيد الأصلي)
- يتم تحديث الرصيد فوراً عند أي عملية تتعلق بالإجازات السنوية
- النظام يمنع تسجيل إجازة سنوية إذا كان الرصيد غير كافي
- جميع العمليات محمية بالمعاملات لضمان عدم فقدان البيانات

مثال على تدفق العمليات:
========================
1. موظف لديه رصيد 30 يوم
2. تسجيل إجازة سنوية ليوم واحد → الرصيد يصبح 29 يوم
3. تعديل الإجازة من "سنوية" إلى "حاضر" → الرصيد يعود إلى 30 يوم
4. حذف سجل إجازة سنوية → الرصيد يزيد بيوم واحد

*/

-- لا حاجة لتنفيذ أي أوامر SQL، هذا ملف توثيقي فقط
