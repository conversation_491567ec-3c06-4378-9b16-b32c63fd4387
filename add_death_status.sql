-- إضافة حالة "وفاة" إلى جدول الحضور
-- تشغيل هذا الملف لإضافة حالة الوفاة إلى النظام

USE employees_db;

-- تحديث جدول الحضور لإضافة حالة "وفاة"
ALTER TABLE attendance MODIFY COLUMN status ENUM('حاضر', 'غائب', 'سنوية', 'بدون راتب', 'استخدام', 'وفاة') NOT NULL;

-- إضافة فهرس لتحسين الأداء (إذا لم يكن موجوداً)
CREATE INDEX IF NOT EXISTS idx_attendance_status ON attendance(status);

-- إضافة عمود employee_user_id لربط المستخدمين بالموظفين (للموظفين الذين يريدون الدخول للنظام)
ALTER TABLE users ADD COLUMN employee_id INT NULL AFTER role;
ALTER TABLE users ADD FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE SET NULL;

COMMIT;
