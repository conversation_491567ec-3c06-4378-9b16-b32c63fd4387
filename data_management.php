<?php
require_once 'config.php';
checkAdmin(); // فقط المدير يمكنه الوصول

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'backup':
                try {
                    $backupFile = 'backup_' . date('Y-m-d_H-i-s') . '.sql';
                    $backupPath = __DIR__ . '/backups/' . $backupFile;
                    
                    // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                    if (!file_exists(__DIR__ . '/backups/')) {
                        mkdir(__DIR__ . '/backups/', 0777, true);
                    }
                    
                    // تصدير قاعدة البيانات
                    $command = "mysqldump --user=" . DB_USER . " --password=" . DB_PASS . " --host=" . DB_HOST . " " . DB_NAME . " > " . $backupPath;
                    
                    // محاولة تنفيذ الأمر
                    $output = null;
                    $return_var = null;
                    exec($command, $output, $return_var);
                    
                    if ($return_var === 0 && file_exists($backupPath)) {
                        $message = 'تم إنشاء النسخة الاحتياطية بنجاح: ' . $backupFile;
                    } else {
                        // طريقة بديلة باستخدام PHP
                        $backup = generateBackupSQL();
                        file_put_contents($backupPath, $backup);
                        $message = 'تم إنشاء النسخة الاحتياطية بنجاح: ' . $backupFile;
                    }
                } catch (Exception $e) {
                    $message = 'خطأ في إنشاء النسخة الاحتياطية: ' . $e->getMessage();
                    $messageType = 'danger';
                }
                break;
                
            case 'import':
                if (isset($_FILES['backup_file']) && $_FILES['backup_file']['error'] == 0) {
                    try {
                        $uploadedFile = $_FILES['backup_file']['tmp_name'];
                        $sql = file_get_contents($uploadedFile);
                        
                        // تنفيذ الاستعلامات
                        $pdo->exec($sql);
                        $message = 'تم استيراد النسخة الاحتياطية بنجاح';
                    } catch (Exception $e) {
                        $message = 'خطأ في استيراد النسخة الاحتياطية: ' . $e->getMessage();
                        $messageType = 'danger';
                    }
                } else {
                    $message = 'يرجى اختيار ملف صحيح';
                    $messageType = 'danger';
                }
                break;
                
            case 'clear_data':
                if (isset($_POST['confirm']) && $_POST['confirm'] == 'yes') {
                    try {
                        $pdo->exec("DELETE FROM attendance");
                        $pdo->exec("DELETE FROM employees WHERE user_id != 1"); // الاحتفاظ بموظفي المدير
                        $message = 'تم حذف البيانات بنجاح';
                    } catch (Exception $e) {
                        $message = 'خطأ في حذف البيانات: ' . $e->getMessage();
                        $messageType = 'danger';
                    }
                } else {
                    $message = 'يجب تأكيد العملية';
                    $messageType = 'warning';
                }
                break;
        }
    }
}

// دالة لإنشاء النسخة الاحتياطية باستخدام PHP
function generateBackupSQL() {
    global $pdo;
    
    $backup = "-- نسخة احتياطية من قاعدة البيانات\n";
    $backup .= "-- تاريخ الإنشاء: " . date('Y-m-d H:i:s') . "\n\n";
    
    $tables = ['users', 'employees', 'attendance'];
    
    foreach ($tables as $table) {
        $backup .= "-- جدول $table\n";
        $backup .= "DROP TABLE IF EXISTS `$table`;\n";
        
        // هيكل الجدول
        $stmt = $pdo->query("SHOW CREATE TABLE `$table`");
        $row = $stmt->fetch();
        $backup .= $row[1] . ";\n\n";
        
        // بيانات الجدول
        $stmt = $pdo->query("SELECT * FROM `$table`");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $backup .= "INSERT INTO `$table` VALUES (";
            $values = array_map(function($value) use ($pdo) {
                return $value === null ? 'NULL' : $pdo->quote($value);
            }, array_values($row));
            $backup .= implode(', ', $values);
            $backup .= ");\n";
        }
        $backup .= "\n";
    }
    
    return $backup;
}

// إحصائيات قاعدة البيانات
$stats = [];
$stats['users'] = $pdo->query("SELECT COUNT(*) FROM users")->fetchColumn();
$stats['employees'] = $pdo->query("SELECT COUNT(*) FROM employees")->fetchColumn();
$stats['attendance'] = $pdo->query("SELECT COUNT(*) FROM attendance")->fetchColumn();

// النسخ الاحتياطية الموجودة
$backups = [];
if (file_exists(__DIR__ . '/backups/')) {
    $backups = array_diff(scandir(__DIR__ . '/backups/'), array('.', '..'));
    $backups = array_filter($backups, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'sql';
    });
    rsort($backups); // ترتيب حسب الأحدث
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة البيانات - مجمع سيد الشهداء (عليه السلام) الخدمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
        }
        .danger-zone {
            border: 2px solid #dc3545;
            border-radius: 10px;
            background-color: #fff5f5;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                مجمع سيد الشهداء (عليه السلام) الخدمي
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-database me-2"></i>إدارة البيانات</h2>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                        <?php echo $message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- إحصائيات قاعدة البيانات -->
                <div class="row mb-4">
                    <div class="col-md-4 mb-3">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $stats['users']; ?></div>
                            <div>المستخدمين</div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $stats['employees']; ?></div>
                            <div>الموظفين</div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $stats['attendance']; ?></div>
                            <div>سجلات الحضور</div>
                        </div>
                    </div>
                </div>

                <!-- النسخ الاحتياطية -->
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-download me-2"></i>النسخ الاحتياطية</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST">
                                    <input type="hidden" name="action" value="backup">
                                    <button type="submit" class="btn btn-success mb-3">
                                        <i class="fas fa-save me-2"></i>إنشاء نسخة احتياطية
                                    </button>
                                </form>

                                <h6>النسخ الاحتياطية الموجودة:</h6>
                                <?php if (empty($backups)): ?>
                                    <p class="text-muted">لا توجد نسخ احتياطية</p>
                                <?php else: ?>
                                    <div class="list-group">
                                        <?php foreach (array_slice($backups, 0, 5) as $backup): ?>
                                            <div class="list-group-item d-flex justify-content-between align-items-center">
                                                <span><?php echo $backup; ?></span>
                                                <a href="backups/<?php echo $backup; ?>" class="btn btn-sm btn-outline-primary" download>
                                                    <i class="fas fa-download"></i>
                                                </a>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-upload me-2"></i>استيراد البيانات</h5>
                            </div>
                            <div class="card-body">
                                <form method="POST" enctype="multipart/form-data">
                                    <input type="hidden" name="action" value="import">
                                    <div class="mb-3">
                                        <label for="backup_file" class="form-label">اختر ملف النسخة الاحتياطية</label>
                                        <input type="file" class="form-control" id="backup_file" name="backup_file" accept=".sql" required>
                                    </div>
                                    <button type="submit" class="btn btn-warning" onclick="return confirm('هل أنت متأكد من استيراد هذه البيانات؟ سيتم استبدال البيانات الحالية.')">
                                        <i class="fas fa-upload me-2"></i>استيراد البيانات
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- منطقة الخطر -->
                <div class="card danger-zone">
                    <div class="card-header bg-danger text-white">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>منطقة الخطر</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-danger"><strong>تحذير:</strong> هذه العمليات لا يمكن التراجع عنها!</p>
                        
                        <form method="POST" class="d-inline">
                            <input type="hidden" name="action" value="clear_data">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="confirm" name="confirm" value="yes" required>
                                <label class="form-check-label text-danger" for="confirm">
                                    أؤكد أنني أريد حذف جميع بيانات الموظفين والحضور
                                </label>
                            </div>
                            <button type="submit" class="btn btn-danger" onclick="return confirm('هل أنت متأكد تماماً؟ سيتم حذف جميع البيانات!')">
                                <i class="fas fa-trash me-2"></i>حذف جميع البيانات
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
