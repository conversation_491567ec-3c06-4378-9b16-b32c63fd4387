<?php
require_once 'config.php';
checkAdmin(); // التحقق من صلاحيات المدير

$message = '';
$messageType = 'success';

// معالجة العمليات
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'add':
                $username = sanitize($_POST['username']);
                $password = $_POST['password'];
                $role = $_POST['role'];
                
                if (empty($username) || empty($password) || empty($role)) {
                    $message = 'جميع الحقول مطلوبة';
                    $messageType = 'danger';
                } elseif (strlen($password) < 6) {
                    $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    $messageType = 'danger';
                } else {
                    try {
                        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("INSERT INTO users (username, password, role) VALUES (?, ?, ?)");
                        $stmt->execute([$username, $hashedPassword, $role]);
                        $message = 'تم إضافة المستخدم بنجاح';
                    } catch(PDOException $e) {
                        if ($e->getCode() == 23000) {
                            $message = 'اسم المستخدم موجود مسبقاً';
                            $messageType = 'danger';
                        } else {
                            $message = 'خطأ في إضافة المستخدم';
                            $messageType = 'danger';
                        }
                    }
                }
                break;
                
            case 'reset_password':
                $user_id = intval($_POST['user_id']);
                $new_password = $_POST['new_password'];
                
                if (empty($new_password)) {
                    $message = 'كلمة المرور الجديدة مطلوبة';
                    $messageType = 'danger';
                } elseif (strlen($new_password) < 6) {
                    $message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    $messageType = 'danger';
                } else {
                    try {
                        $hashedPassword = password_hash($new_password, PASSWORD_DEFAULT);
                        $stmt = $pdo->prepare("UPDATE users SET password = ? WHERE id = ? AND id != ?");
                        $stmt->execute([$hashedPassword, $user_id, $_SESSION['user_id']]);
                        
                        if ($stmt->rowCount() > 0) {
                            $message = 'تم إعادة تعيين كلمة المرور بنجاح';
                        } else {
                            $message = 'لا يمكن إعادة تعيين كلمة مرور حسابك الخاص';
                            $messageType = 'danger';
                        }
                    } catch(PDOException $e) {
                        $message = 'خطأ في إعادة تعيين كلمة المرور';
                        $messageType = 'danger';
                    }
                }
                break;
                
            case 'delete':
                $user_id = intval($_POST['user_id']);
                
                if ($user_id == $_SESSION['user_id']) {
                    $message = 'لا يمكن حذف حسابك الخاص';
                    $messageType = 'danger';
                } else {
                    try {
                        $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
                        $stmt->execute([$user_id]);
                        $message = 'تم حذف المستخدم بنجاح';
                    } catch(PDOException $e) {
                        $message = 'خطأ في حذف المستخدم';
                        $messageType = 'danger';
                    }
                }
                break;
        }
    }
}

// جلب المستخدمين مع إحصائيات الموظفين
$stmt = $pdo->query("
    SELECT u.*, 
           COUNT(e.id) as employees_count
    FROM users u 
    LEFT JOIN employees e ON u.id = e.user_id 
    GROUP BY u.id 
    ORDER BY u.created_at DESC
");
$users = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - نظام إدارة الموظفين</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .role-badge {
            font-size: 0.85rem;
            padding: 0.5rem 1rem;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                نظام إدارة الموظفين
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <a class="nav-link" href="employees.php">
                    <i class="fas fa-users me-1"></i>الموظفين
                </a>
                <a class="nav-link" href="attendance.php">
                    <i class="fas fa-calendar-alt me-1"></i>الحضور
                </a>
                <a class="nav-link" href="reports.php">
                    <i class="fas fa-chart-bar me-1"></i>التقارير
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                        <span class="badge bg-warning text-dark ms-1">مدير</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-cog me-2"></i>إدارة المستخدمين</h2>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                    </button>
                </div>

                <?php if ($message): ?>
                    <?php echo showMessage($message, $messageType); ?>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>اسم المستخدم</th>
                                        <th>الدور</th>
                                        <th>عدد الموظفين</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($users as $user): ?>
                                        <tr>
                                            <td>
                                                <i class="fas fa-user me-2 text-primary"></i>
                                                <?php echo htmlspecialchars($user['username']); ?>
                                                <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                                    <span class="badge bg-info ms-2">أنت</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($user['role'] == 'admin'): ?>
                                                    <span class="badge role-badge bg-warning text-dark">
                                                        <i class="fas fa-crown me-1"></i>مدير
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge role-badge bg-primary">
                                                        <i class="fas fa-user me-1"></i>مستخدم
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">
                                                    <?php echo $user['employees_count']; ?> موظف
                                                </span>
                                            </td>
                                            <td><?php echo date('Y-m-d', strtotime($user['created_at'])); ?></td>
                                            <td>
                                                <?php if ($user['id'] != $_SESSION['user_id']): ?>
                                                    <button class="btn btn-sm btn-outline-warning me-1" 
                                                            onclick="resetPassword(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>')">
                                                        <i class="fas fa-key"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-danger" 
                                                            onclick="deleteUser(<?php echo $user['id']; ?>, '<?php echo htmlspecialchars($user['username']); ?>', <?php echo $user['employees_count']; ?>)">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-muted">لا يمكن التعديل</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نموذج إضافة مستخدم -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="add">
                        <div class="mb-3">
                            <label for="username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">كلمة المرور</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                        </div>
                        <div class="mb-3">
                            <label for="role" class="form-label">الدور</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="user">مستخدم عادي</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-primary">إضافة المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج إعادة تعيين كلمة المرور -->
    <div class="modal fade" id="resetPasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إعادة تعيين كلمة المرور</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="reset_password">
                        <input type="hidden" name="user_id" id="reset_user_id">
                        <p>إعادة تعيين كلمة المرور للمستخدم: <strong id="reset_username"></strong></p>
                        <div class="mb-3">
                            <label for="new_password" class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                            <div class="form-text">يجب أن تكون كلمة المرور 6 أحرف على الأقل</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-warning">إعادة تعيين كلمة المرور</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- نموذج حذف مستخدم -->
    <div class="modal fade" id="deleteUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تأكيد الحذف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="user_id" id="delete_user_id">
                        <p>هل أنت متأكد من حذف المستخدم <strong id="delete_username"></strong>؟</p>
                        <div class="alert alert-warning" id="delete_warning" style="display: none;">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            سيتم حذف <span id="employees_count"></span> موظف وجميع سجلات الحضور المرتبطة بهم
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="submit" class="btn btn-danger">حذف المستخدم</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function resetPassword(userId, username) {
            document.getElementById('reset_user_id').value = userId;
            document.getElementById('reset_username').textContent = username;
            document.getElementById('new_password').value = '';
            new bootstrap.Modal(document.getElementById('resetPasswordModal')).show();
        }

        function deleteUser(userId, username, employeesCount) {
            document.getElementById('delete_user_id').value = userId;
            document.getElementById('delete_username').textContent = username;

            const warningDiv = document.getElementById('delete_warning');
            if (employeesCount > 0) {
                document.getElementById('employees_count').textContent = employeesCount;
                warningDiv.style.display = 'block';
            } else {
                warningDiv.style.display = 'none';
            }

            new bootstrap.Modal(document.getElementById('deleteUserModal')).show();
        }
    </script>
</body>
</html>
