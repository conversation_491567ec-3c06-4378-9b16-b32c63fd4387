<?php
require_once 'config.php';
checkLogin();

// التحقق من وجود معرف الموظف في الجلسة أو الحصول عليه
$employee_id = null;
$employee_data = null;

// إذا كان المستخدم موظف عادي، البحث عن بياناته
if ($_SESSION['role'] == 'user') {
    // البحث أولاً في جدول المستخدمين عن employee_id
    $user_stmt = $pdo->prepare("SELECT employee_id FROM users WHERE id = ?");
    $user_stmt->execute([$_SESSION['user_id']]);
    $user_data = $user_stmt->fetch();

    if ($user_data && $user_data['employee_id']) {
        // إذا كان هناك ربط مباشر
        $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = ?");
        $stmt->execute([$user_data['employee_id']]);
        $employee_data = $stmt->fetch();
        if ($employee_data) {
            $employee_id = $employee_data['id'];
        }
    } else {
        // البحث بالطريقة القديمة (user_id في جدول employees)
        $stmt = $pdo->prepare("SELECT * FROM employees WHERE user_id = ? LIMIT 1");
        $stmt->execute([$_SESSION['user_id']]);
        $employee_data = $stmt->fetch();
        if ($employee_data) {
            $employee_id = $employee_data['id'];
        }
    }

    if (!$employee_data) {
        $error_message = "لم يتم العثور على بيانات الموظف. يرجى التواصل مع الإدارة لربط حسابك ببيانات الموظف.";
    }
} else {
    // إذا كان المدير، يمكنه اختيار موظف لعرض بياناته
    if (isset($_GET['emp_id'])) {
        $employee_id = intval($_GET['emp_id']);
        $stmt = $pdo->prepare("SELECT * FROM employees WHERE id = ?");
        $stmt->execute([$employee_id]);
        $employee_data = $stmt->fetch();
    }
    
    // جلب قائمة الموظفين للمدير
    $employees_stmt = $pdo->query("SELECT id, name, employee_id FROM employees ORDER BY name");
    $all_employees = $employees_stmt->fetchAll();
}

// جلب سجلات الحضور والإجازات إذا تم تحديد موظف
$attendance_records = [];
$leave_summary = [];
$current_month_stats = [];

if ($employee_id && $employee_data) {
    // جلب سجلات الحضور للشهر الحالي
    $current_month = date('Y-m');
    $stmt = $pdo->prepare("
        SELECT date, status 
        FROM attendance 
        WHERE employee_id = ? AND DATE_FORMAT(date, '%Y-%m') = ?
        ORDER BY date DESC
    ");
    $stmt->execute([$employee_id, $current_month]);
    $attendance_records = $stmt->fetchAll();
    
    // حساب إحصائيات الشهر الحالي
    $stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN status = 'حاضر' THEN 1 END) as present_days,
            COUNT(CASE WHEN status = 'غائب' THEN 1 END) as absent_days,
            COUNT(CASE WHEN status = 'سنوية' THEN 1 END) as annual_leave,
            COUNT(CASE WHEN status = 'بدون راتب' THEN 1 END) as unpaid_leave,
            COUNT(CASE WHEN status = 'استخدام' THEN 1 END) as usage_days
        FROM attendance 
        WHERE employee_id = ? AND DATE_FORMAT(date, '%Y-%m') = ?
    ");
    $stats_stmt->execute([$employee_id, $current_month]);
    $current_month_stats = $stats_stmt->fetch();
    
    // حساب ملخص الإجازات للسنة الحالية
    $current_year = date('Y');
    $year_stats_stmt = $pdo->prepare("
        SELECT 
            COUNT(CASE WHEN status = 'سنوية' THEN 1 END) as total_annual_used,
            COUNT(CASE WHEN status = 'بدون راتب' THEN 1 END) as total_unpaid_used
        FROM attendance 
        WHERE employee_id = ? AND YEAR(date) = ?
    ");
    $year_stats_stmt->execute([$employee_id, $current_year]);
    $year_stats = $year_stats_stmt->fetch();
    
    // حساب الرصيد المتبقي
    $remaining_balance = $employee_data['balance'] - $year_stats['total_annual_used'];
    
    $leave_summary = [
        'annual_balance' => $employee_data['balance'],
        'annual_used' => $year_stats['total_annual_used'],
        'annual_remaining' => max(0, $remaining_balance),
        'unpaid_used' => $year_stats['total_unpaid_used']
    ];
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>بوابة الموظف - مجمع سيد الشهداء (عليه السلام) الخدمي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background-color: #f8f9fa;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stat-card {
            text-align: center;
            padding: 20px;
            border-radius: 10px;
            color: white;
        }
        .stat-card.balance {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card.used {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card.remaining {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.unpaid {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
        }
        .employee-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-users me-2"></i>
                مجمع سيد الشهداء (عليه السلام) الخدمي
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-home me-1"></i>الرئيسية
                </a>
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user me-1"></i><?php echo $_SESSION['username']; ?>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="logout.php">
                            <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-circle me-2"></i>بوابة الموظف</h2>
                </div>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($_SESSION['role'] == 'admin'): ?>
                    <!-- اختيار الموظف للمدير -->
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5>اختيار الموظف</h5>
                            <form method="GET">
                                <div class="row">
                                    <div class="col-md-8">
                                        <select class="form-select" name="emp_id" required>
                                            <option value="">اختر الموظف</option>
                                            <?php foreach ($all_employees as $emp): ?>
                                                <option value="<?php echo $emp['id']; ?>" 
                                                        <?php echo (isset($_GET['emp_id']) && $_GET['emp_id'] == $emp['id']) ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($emp['name']) . ' - ' . htmlspecialchars($emp['employee_id']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search me-1"></i>عرض البيانات
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($employee_data): ?>
                    <!-- معلومات الموظف -->
                    <div class="employee-info mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <h4><i class="fas fa-user me-2"></i><?php echo htmlspecialchars($employee_data['name']); ?></h4>
                                <p><strong>معرف الموظف:</strong> <?php echo htmlspecialchars($employee_data['employee_id']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>الشعبة:</strong> <?php echo htmlspecialchars($employee_data['department']); ?></p>
                                <p><strong>تاريخ الانضمام:</strong> <?php echo date('Y-m-d', strtotime($employee_data['created_at'])); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- ملخص الإجازات -->
                    <div class="row mb-4">
                        <div class="col-md-3 mb-3">
                            <div class="stat-card balance">
                                <div class="stat-number"><?php echo $leave_summary['annual_balance']; ?></div>
                                <div>الرصيد السنوي</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-card used">
                                <div class="stat-number"><?php echo $leave_summary['annual_used']; ?></div>
                                <div>المستخدم</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-card remaining">
                                <div class="stat-number"><?php echo $leave_summary['annual_remaining']; ?></div>
                                <div>المتبقي</div>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="stat-card unpaid">
                                <div class="stat-number"><?php echo $leave_summary['unpaid_used']; ?></div>
                                <div>بدون راتب</div>
                            </div>
                        </div>
                    </div>

                    <!-- إحصائيات الشهر الحالي -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-calendar-alt me-2"></i>إحصائيات الشهر الحالي (<?php echo date('Y-m'); ?>)</h5>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-md-2">
                                    <div class="p-3">
                                        <div class="h4 text-success"><?php echo $current_month_stats['present_days']; ?></div>
                                        <small>حاضر</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="p-3">
                                        <div class="h4 text-danger"><?php echo $current_month_stats['absent_days']; ?></div>
                                        <small>غائب</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="p-3">
                                        <div class="h4 text-info"><?php echo $current_month_stats['annual_leave']; ?></div>
                                        <small>إجازة سنوية</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="p-3">
                                        <div class="h4 text-warning"><?php echo $current_month_stats['unpaid_leave']; ?></div>
                                        <small>بدون راتب</small>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="p-3">
                                        <div class="h4 text-secondary"><?php echo $current_month_stats['usage_days']; ?></div>
                                        <small>استخدام</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- سجلات الحضور الأخيرة -->
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list me-2"></i>سجلات الحضور الأخيرة</h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($attendance_records)): ?>
                                <div class="text-center py-4">
                                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">لا توجد سجلات حضور للشهر الحالي</h5>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>التاريخ</th>
                                                <th>اليوم</th>
                                                <th>الحالة</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($attendance_records as $record): ?>
                                                <tr>
                                                    <td><?php echo date('Y-m-d', strtotime($record['date'])); ?></td>
                                                    <td><?php echo date('l', strtotime($record['date'])); ?></td>
                                                    <td>
                                                        <?php
                                                        $statusClass = '';
                                                        $statusIcon = '';
                                                        switch ($record['status']) {
                                                            case 'حاضر':
                                                                $statusClass = 'bg-success';
                                                                $statusIcon = 'fas fa-check';
                                                                break;
                                                            case 'غائب':
                                                                $statusClass = 'bg-danger';
                                                                $statusIcon = 'fas fa-times';
                                                                break;
                                                            case 'سنوية':
                                                                $statusClass = 'bg-info';
                                                                $statusIcon = 'fas fa-umbrella-beach';
                                                                break;
                                                            case 'بدون راتب':
                                                                $statusClass = 'bg-warning text-dark';
                                                                $statusIcon = 'fas fa-ban';
                                                                break;
                                                            case 'استخدام':
                                                                $statusClass = 'bg-secondary';
                                                                $statusIcon = 'fas fa-briefcase';
                                                                break;
                                                            case 'وفاة':
                                                                $statusClass = 'bg-dark';
                                                                $statusIcon = 'fas fa-cross';
                                                                break;
                                                        }
                                                        ?>
                                                        <span class="badge <?php echo $statusClass; ?>">
                                                            <i class="<?php echo $statusIcon; ?> me-1"></i>
                                                            <?php echo $record['status']; ?>
                                                        </span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php elseif ($_SESSION['role'] == 'user'): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        لم يتم العثور على بيانات الموظف. يرجى التواصل مع الإدارة لربط حسابك ببيانات الموظف.
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
