# نظام إدارة الموظفين - مجمع سيد الشهداء (عليه السلام) الخدمي

نظام شامل لإدارة الموظفين وسجلات الحضور والغياب مبني بلغة PHP ويعمل على XAMPP.

## المميزات

### تسجيل الدخول والأمان
- نظام تسجيل دخول آمن
- حماية الجلسات (Sessions)
- تشفير كلمات المرور
- صلاحيات مختلفة للمدير والمستخدمين

### إدارة الموظفين
- إضافة موظف جديد (الاسم، معرف الموظف، الشعبة، الرصيد بالأيام)
- تعديل بيانات الموظف (الاسم والرصيد فقط)
- حذف الموظفين
- كل مستخدم يرى موظفيه فقط (عدا المدير)

### سجلات الحضور والغياب
- إضافة سجل حضور/غياب لكل موظف
- ست حالات: حاضر، غائب، سنوية، بدون راتب، استخدام، وفاة
- **نظام الرصيد التلقائي**: استقطاع تلقائي من رصيد الموظف عند تسجيل إجازة سنوية
- عرض السجلات مع إمكانية الفلترة
- تعديل وحذف السجلات مع تحديث الرصيد تلقائياً

### التقارير
- تقارير يومية وأسبوعية وشهرية وسنوية
- تقارير مخصصة حسب الفترة الزمنية
- إحصائيات شاملة لكل موظف
- تفاصيل يومية لجميع السجلات
- طباعة محسنة بحجم A4 مع تنسيق احترافي

### نظام الرصيد التلقائي
- **استقطاع تلقائي**: عند تسجيل إجازة سنوية يتم استقطاع يوم من رصيد الموظف
- **حماية من السحب على المكشوف**: منع تسجيل إجازة سنوية إذا كان الرصيد غير كافي
- **تحديث ذكي**: عند تعديل أو حذف إجازة سنوية يتم تحديث الرصيد تلقائياً
- **تحذيرات واضحة**: عرض الرصيد الحالي وتحذيرات عند الرصيد المنخفض
- **بوابة الموظف**: عرض الرصيد المتبقي والمستخدم لكل موظف

### صلاحيات المدير
- إضافة مستخدمين جدد للنظام
- إعادة تعيين كلمة مرور لأي مستخدم
- عرض جميع الموظفين وجميع السجلات
- حذف المستخدمين
- إدارة البيانات والنسخ الاحتياطية
- ربط المستخدمين بالموظفين

## متطلبات التشغيل

- XAMPP (Apache + MySQL + PHP)
- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- متصفح ويب حديث

## طريقة التثبيت

### 1. تحضير البيئة
1. تأكد من تشغيل XAMPP
2. تأكد من تشغيل Apache و MySQL

### 2. إعداد الملفات
1. انسخ جميع الملفات إلى مجلد `htdocs/employees` في XAMPP
2. أو ضعها في أي مجلد فرعي داخل `htdocs`

### 3. إعداد قاعدة البيانات
1. افتح phpMyAdmin من خلال `http://localhost/phpmyadmin`
2. أنشئ قاعدة بيانات جديدة باسم `employees_db`
3. استورد ملف `database.sql` أو نفذ الأوامر التالية:

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS employees_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE employees_db;

-- جدول المستخدمين
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الموظفين
CREATE TABLE employees (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    employee_id VARCHAR(50) UNIQUE NOT NULL COMMENT 'معرف الموظف',
    department VARCHAR(100) NOT NULL COMMENT 'الشعبة',
    balance INT DEFAULT 0 COMMENT 'الرصيد بالأيام',
    user_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الحضور والغياب
CREATE TABLE attendance (
    id INT AUTO_INCREMENT PRIMARY KEY,
    employee_id INT NOT NULL,
    date DATE NOT NULL,
    status ENUM('حاضر', 'غائب', 'سنوية', 'بدون راتب', 'استخدام', 'وفاة') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (employee_id) REFERENCES employees(id) ON DELETE CASCADE,
    UNIQUE KEY unique_employee_date (employee_id, date)
);

-- إدراج المدير الافتراضي
INSERT INTO users (username, password, role) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin');

-- إدراج مستخدم تجريبي
INSERT INTO users (username, password, role) VALUES 
('user1', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user');
```

### 4. تشغيل النظام
1. افتح المتصفح واذهب إلى `http://localhost/employees/login.php`
2. استخدم أحد الحسابات التجريبية:
   - **المدير**: `admin` / `password`
   - **مستخدم عادي**: `user1` / `password`

## هيكل الملفات

```
employees/
├── config.php          # إعدادات قاعدة البيانات والدوال المساعدة
├── login.php           # صفحة تسجيل الدخول
├── logout.php          # تسجيل الخروج
├── dashboard.php       # لوحة التحكم الرئيسية
├── employees.php       # إدارة الموظفين
├── attendance.php      # سجلات الحضور والغياب
├── reports.php         # التقارير (يومية، أسبوعية، شهرية، سنوية)
├── users.php           # إدارة المستخدمين (للمدير فقط)
├── database.sql        # هيكل قاعدة البيانات
└── README.md           # هذا الملف
```

## الحسابات الافتراضية

### حساب المدير
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `password`
- **الصلاحيات**: جميع الصلاحيات

### حساب المستخدم العادي
- **اسم المستخدم**: `user1`
- **كلمة المرور**: `password`
- **الصلاحيات**: إدارة موظفيه فقط

## الأمان

- جميع كلمات المرور مشفرة باستخدام `password_hash()`
- حماية من SQL Injection باستخدام Prepared Statements
- التحقق من الصلاحيات في كل صفحة
- تنظيف البيانات المدخلة
- حماية الجلسات

## التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL
- **Frontend**: HTML5, CSS3, JavaScript
- **Framework**: Bootstrap 5.3
- **Icons**: Font Awesome 6.0
- **Fonts**: Google Fonts (Cairo)

## التحديثات

### إضافة حالة "وفاة" (الإصدار الحالي)

تم إضافة حالة جديدة "وفاة" إلى سجل الحضور:

1. **تحديث قاعدة البيانات**: تشغيل ملف `add_death_status.sql`
2. **تحديث النماذج**: إضافة خيار "وفاة" في نماذج الحضور
3. **تحديث التقارير**: إضافة عمود "وفاة" في جميع التقارير
4. **تحديث الإحصائيات**: إضافة بطاقة إحصائية للوفاة

لتطبيق التحديث على قاعدة بيانات موجودة:
```sql
-- تشغيل هذا الأمر في phpMyAdmin أو MySQL
ALTER TABLE attendance MODIFY COLUMN status ENUM('حاضر', 'غائب', 'سنوية', 'بدون راتب', 'استخدام', 'وفاة') NOT NULL;
```

## الدعم والمساعدة

إذا واجهت أي مشاكل:

1. تأكد من تشغيل Apache و MySQL
2. تأكد من إنشاء قاعدة البيانات بشكل صحيح
3. تحقق من إعدادات قاعدة البيانات في `config.php`
4. تأكد من وضع الملفات في المجلد الصحيح

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
